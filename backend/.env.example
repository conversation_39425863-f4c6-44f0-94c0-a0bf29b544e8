# Environment Configuration Example
# Copy this file to .env and update the values for your environment

# Application Settings
PROJECT_NAME="Warehouse Management System"
VERSION="1.0.0"
DEBUG=false
HOST=0.0.0.0
PORT=8000

# Database Settings
POSTGRES_SERVER=localhost
POSTGRES_PORT=5433
POSTGRES_USER=warehouse
POSTGRES_PASSWORD=warehouse_pass
POSTGRES_DB=warehouse_db

# Database Pool Settings
DB_POOL_MIN_SIZE=5
DB_POOL_MAX_SIZE=20
DB_POOL_MAX_QUERIES=50000
DB_POOL_MAX_INACTIVE_CONNECTION_LIFETIME=300.0

# CORS Settings
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://localhost:8000"]

# Logging Settings
LOG_LEVEL=INFO
LOG_FORMAT=json

# API Settings
API_RATE_LIMIT=100/minute
MAX_PAGE_SIZE=1000
DEFAULT_PAGE_SIZE=50

# Security Settings
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
